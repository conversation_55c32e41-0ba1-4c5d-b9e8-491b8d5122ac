import { But<PERSON> } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@workspace/ui/components/dialog";
import { Input } from "@workspace/ui/components/input";
import { Label } from "@workspace/ui/components/label";
import { Separator } from "@workspace/ui/components/separator";

export default async function DialogDemo() {
  return (
    <Dialog open>
      <form>
        <DialogContent className="sm:max-w-[512px]">
          <DialogHeader>
            <DialogTitle>{"Profili Düzenle"}</DialogTitle>
            <DialogDescription>
              {
                "Profilinizde değişiklik yapabilirsiniz. İşiniz bittiğinde kaydet butonuna tıklayın."
              }
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 mx-6 p-4 rounded-lg bg-card-foreground/10">
            <div className="grid grid-cols-3 gap-3">
              <Label htmlFor="name-1">{"İsim"}</Label>
              <Input
                className="col-span-2"
                id="name-1"
                name="name"
                defaultValue="Pedro Duarte"
              />
            </div>
            <Separator />
            <div className="grid grid-cols-3 gap-3">
              <Label htmlFor="username-1">{"Kullanıcı Adı"}</Label>
              <Input
                className="col-span-2"
                id="username-1"
                name="username"
                defaultValue="@peduarte"
              />
            </div>
          </div>
          <DialogFooter>
            <DialogClose asChild>
              <Button variant={"primary"}>{"KAYDET"}</Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </form>
    </Dialog>
  );
}
